<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LogoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_logout_route_exists(): void
    {
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('logout'));
    }

    public function test_authenticated_user_can_logout_via_post(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->post(route('logout'));

        $response->assertRedirect('/');
        $this->assertGuest();
    }

    public function test_guest_cannot_access_logout_route(): void
    {
        $response = $this->post(route('logout'));

        $response->assertRedirect(route('login'));
    }

    public function test_logout_invalidates_session(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);
        
        // Verify user is authenticated
        $this->assertAuthenticated();
        
        // Store session ID before logout
        $sessionId = session()->getId();
        
        // Logout
        $response = $this->post(route('logout'));
        
        // Verify user is logged out
        $this->assertGuest();
        
        // Verify session was invalidated (new session ID)
        $this->assertNotEquals($sessionId, session()->getId());
    }

    public function test_logout_with_csrf_protection(): void
    {
        $user = User::factory()->create();

        // Test without CSRF token (should fail)
        $response = $this->actingAs($user)
            ->post(route('logout'), [], ['X-CSRF-TOKEN' => 'invalid']);

        $response->assertStatus(419); // CSRF token mismatch

        // Test with valid CSRF token
        $response = $this->actingAs($user)
            ->post(route('logout'));

        $response->assertRedirect('/');
        $this->assertGuest();
    }
}
