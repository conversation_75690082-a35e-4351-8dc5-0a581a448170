<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class InvoiceItemController extends Controller
{
    /**
     * Store a newly created invoice item.
     */
    public function store(Request $request, Invoice $invoice): JsonResponse|RedirectResponse
    {
        $this->authorize('update', $invoice);

        $validator = Validator::make($request->all(), [
            'service_id' => 'nullable|exists:technical_services,id',
            'item_name_ar' => 'required|string|max:255',
            'item_name_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'quantity' => 'required|numeric|min:0.01',
            'unit_ar' => 'required|string|max:50',
            'unit_en' => 'nullable|string|max:50',
            'unit_price' => 'required|numeric|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();
        $validated['invoice_id'] = $invoice->id;

        // Calculate amounts
        $subtotal = $validated['quantity'] * $validated['unit_price'];
        $discountAmount = isset($validated['discount_rate']) 
            ? ($subtotal * $validated['discount_rate']) / 100 
            : ($validated['discount_amount'] ?? 0);
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = isset($validated['tax_rate']) 
            ? ($taxableAmount * $validated['tax_rate']) / 100 
            : 0;

        $validated['discount_amount'] = $discountAmount;
        $validated['tax_amount'] = $taxAmount;
        $validated['total_amount'] = $taxableAmount + $taxAmount;

        // Set sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = $invoice->items()->max('sort_order') + 1;
        }

        $item = $invoice->items()->create($validated);

        // Recalculate invoice totals
        $invoice->calculateTotals();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'تم إضافة العنصر بنجاح',
                'item' => $item->load('service'),
                'invoice_totals' => [
                    'subtotal' => $invoice->subtotal,
                    'discount_amount' => $invoice->discount_amount,
                    'tax_amount' => $invoice->tax_amount,
                    'total_amount' => $invoice->total_amount,
                ]
            ]);
        }

        return redirect()->back()
            ->with('success', 'تم إضافة العنصر بنجاح');
    }

    /**
     * Update the specified invoice item.
     */
    public function update(Request $request, Invoice $invoice, InvoiceItem $item): JsonResponse|RedirectResponse
    {
        $this->authorize('update', $invoice);

        // Ensure the item belongs to the invoice
        if ($item->invoice_id !== $invoice->id) {
            abort(404);
        }

        $validator = Validator::make($request->all(), [
            'service_id' => 'nullable|exists:technical_services,id',
            'item_name_ar' => 'required|string|max:255',
            'item_name_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'quantity' => 'required|numeric|min:0.01',
            'unit_ar' => 'required|string|max:50',
            'unit_en' => 'nullable|string|max:50',
            'unit_price' => 'required|numeric|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        // Calculate amounts
        $subtotal = $validated['quantity'] * $validated['unit_price'];
        $discountAmount = isset($validated['discount_rate']) 
            ? ($subtotal * $validated['discount_rate']) / 100 
            : ($validated['discount_amount'] ?? 0);
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = isset($validated['tax_rate']) 
            ? ($taxableAmount * $validated['tax_rate']) / 100 
            : 0;

        $validated['discount_amount'] = $discountAmount;
        $validated['tax_amount'] = $taxAmount;
        $validated['total_amount'] = $taxableAmount + $taxAmount;

        $item->update($validated);

        // Recalculate invoice totals
        $invoice->calculateTotals();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'تم تحديث العنصر بنجاح',
                'item' => $item->fresh()->load('service'),
                'invoice_totals' => [
                    'subtotal' => $invoice->fresh()->subtotal,
                    'discount_amount' => $invoice->discount_amount,
                    'tax_amount' => $invoice->tax_amount,
                    'total_amount' => $invoice->total_amount,
                ]
            ]);
        }

        return redirect()->back()
            ->with('success', 'تم تحديث العنصر بنجاح');
    }

    /**
     * Remove the specified invoice item.
     */
    public function destroy(Request $request, Invoice $invoice, InvoiceItem $item): JsonResponse|RedirectResponse
    {
        $this->authorize('update', $invoice);

        // Ensure the item belongs to the invoice
        if ($item->invoice_id !== $invoice->id) {
            abort(404);
        }

        $item->delete();

        // Recalculate invoice totals
        $invoice->calculateTotals();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'تم حذف العنصر بنجاح',
                'invoice_totals' => [
                    'subtotal' => $invoice->fresh()->subtotal,
                    'discount_amount' => $invoice->discount_amount,
                    'tax_amount' => $invoice->tax_amount,
                    'total_amount' => $invoice->total_amount,
                ]
            ]);
        }

        return redirect()->back()
            ->with('success', 'تم حذف العنصر بنجاح');
    }
}
