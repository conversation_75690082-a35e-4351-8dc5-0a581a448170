<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['items' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['items' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
// Auto-generate breadcrumb items based on current route if not provided
if (empty($items)) {
    $routeName = request()->route()->getName();
    $segments = explode('.', $routeName);
    
    $items = [];
    $currentPath = '';
    
    foreach ($segments as $index => $segment) {
        $currentPath .= ($index > 0 ? '.' : '') . $segment;
        
        // Skip the last segment as it's the current page
        if ($index === count($segments) - 1) {
            continue;
        }
        
        $label = match($segment) {
            'dashboard' => __('app.navigation.dashboard'),
            'clients' => __('app.navigation.clients'),
            'projects' => __('app.navigation.projects'),
            'finance' => __('app.navigation.finance'),
            'team' => __('app.navigation.team'),
            'services' => __('app.navigation.technical_services'),
            'reports' => __('app.navigation.reports'),
            'settings' => __('app.navigation.settings'),
            default => ucfirst($segment)
        };
        
        $items[] = [
            'label' => $label,
            'url' => route($currentPath),
        ];
    }
}
?>

<?php if(!empty($items)): ?>
<nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2 rtl:space-x-reverse">
        
        <li>
            <div>
                <a href="<?php echo e(route('dashboard')); ?>" class="text-secondary-400 hover:text-secondary-500">
                    <?php if (isset($component)) { $__componentOriginalce262628e3a8d44dc38fd1f3965181bc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalce262628e3a8d44dc38fd1f3965181bc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.icon','data' => ['name' => 'home','class' => 'flex-shrink-0 h-4 w-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'home','class' => 'flex-shrink-0 h-4 w-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalce262628e3a8d44dc38fd1f3965181bc)): ?>
<?php $attributes = $__attributesOriginalce262628e3a8d44dc38fd1f3965181bc; ?>
<?php unset($__attributesOriginalce262628e3a8d44dc38fd1f3965181bc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalce262628e3a8d44dc38fd1f3965181bc)): ?>
<?php $component = $__componentOriginalce262628e3a8d44dc38fd1f3965181bc; ?>
<?php unset($__componentOriginalce262628e3a8d44dc38fd1f3965181bc); ?>
<?php endif; ?>
                    <span class="sr-only"><?php echo e(__('app.navigation.dashboard')); ?></span>
                </a>
            </div>
        </li>

        
        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-4 w-4 text-secondary-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="<?php echo e($item['url']); ?>" class="text-sm font-medium text-secondary-500 hover:text-secondary-700 font-arabic">
                        <?php echo e($item['label']); ?>

                    </a>
                </div>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        
        <li>
            <div class="flex items-center">
                <svg class="flex-shrink-0 h-4 w-4 text-secondary-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm font-medium text-secondary-900 font-arabic">
                    <?php if(request()->route()->getName() === 'dashboard'): ?>
                        <?php echo e(__('app.navigation.dashboard')); ?>

                    <?php else: ?>
                        <?php echo e($title ?? 'الصفحة الحالية'); ?>

                    <?php endif; ?>
                </span>
            </div>
        </li>
    </ol>
</nav>
<?php endif; ?>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/components/breadcrumb.blade.php ENDPATH**/ ?>